/**
 * 🚀 PREMIUM OTP AUTHENTICATION - ENHANCED JAVASCRIPT 🚀
 * Features: Stunning animations, premium interactions, and captivating UX
 * Version: 2.0 Premium Edition
 */

// Premium Page Loader
class PremiumLoader {
  constructor() {
    this.createLoader();
  }

  createLoader() {
    const loader = document.createElement("div");
    loader.id = "premium-loader";
    loader.innerHTML = `
      <div class="loader-content">
        <div class="loader-logo">
          <i class="fas fa-shield-alt"></i>
        </div>
        <div class="loader-text">Premium Auth</div>
        <div class="loader-progress">
          <div class="loader-bar"></div>
        </div>
      </div>
    `;
    loader.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 50%, #4cc9f0 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      transition: opacity 0.5s ease;
    `;

    const style = document.createElement("style");
    style.textContent = `
      .loader-content {
        text-align: center;
        color: white;
      }
      .loader-logo {
        font-size: 4rem;
        margin-bottom: 1rem;
        animation: pulse 2s infinite;
      }
      .loader-text {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 2rem;
        font-family: 'Poppins', sans-serif;
      }
      .loader-progress {
        width: 200px;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
        overflow: hidden;
        margin: 0 auto;
      }
      .loader-bar {
        height: 100%;
        background: white;
        border-radius: 2px;
        animation: loading 2s ease-in-out;
      }
      @keyframes loading {
        0% { width: 0%; }
        100% { width: 100%; }
      }
    `;

    document.head.appendChild(style);
    document.body.appendChild(loader);

    setTimeout(() => {
      loader.style.opacity = "0";
      setTimeout(() => loader.remove(), 500);
    }, 2000);
  }
}

// Premium Particle System
class PremiumParticles {
  constructor() {
    this.canvas = document.createElement("canvas");
    this.ctx = this.canvas.getContext("2d");
    this.particles = [];
    this.init();
  }

  init() {
    this.canvas.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      opacity: 0.6;
    `;

    document.body.appendChild(this.canvas);
    this.resize();
    this.createParticles();
    this.animate();

    window.addEventListener("resize", () => this.resize());
  }

  resize() {
    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;
  }

  createParticles() {
    for (let i = 0; i < 50; i++) {
      this.particles.push({
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        size: Math.random() * 3 + 1,
        opacity: Math.random() * 0.5 + 0.2
      });
    }
  }

  animate() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    this.particles.forEach(particle => {
      particle.x += particle.vx;
      particle.y += particle.vy;

      if (particle.x < 0 || particle.x > this.canvas.width) particle.vx *= -1;
      if (particle.y < 0 || particle.y > this.canvas.height) particle.vy *= -1;

      this.ctx.beginPath();
      this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      this.ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
      this.ctx.fill();
    });

    requestAnimationFrame(() => this.animate());
  }
}

// Modern Notification System
class ModernNotifications {
  constructor() {
    this.container = this.createContainer();
    this.sounds = {
      success: this.createSound(
        "data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT"
      ),
      error: this.createSound(
        "data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT"
      ),
      info: this.createSound(
        "data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT"
      )
    };
  }

  createContainer() {
    let container = document.querySelector(".toast-container");
    if (!container) {
      container = document.createElement("div");
      container.className = "toast-container";
      document.body.appendChild(container);
    }
    return container;
  }

  createSound(dataUri) {
    try {
      return new Audio(dataUri);
    } catch (e) {
      return null;
    }
  }

  show(message, type = "info", options = {}) {
    const {
      title = this.getDefaultTitle(type),
      emoji = this.getDefaultEmoji(type),
      duration = 10000,
      sound = true,
      vibrate = true,
      progress = true
    } = options;

    // Play premium sound
    if (sound && this.sounds[type]) {
      this.sounds[type].play().catch(() => {});
    }

    // Premium vibration patterns
    if (vibrate && navigator.vibrate) {
      navigator.vibrate(this.getVibrationPattern(type));
    }

    // Create premium toast element
    const toast = this.createToast(
      title,
      message,
      type,
      emoji,
      duration,
      progress
    );
    this.container.appendChild(toast);

    // Add entrance animation
    toast.style.transform = "translateX(100%) scale(0.8)";
    toast.style.opacity = "0";

    setTimeout(() => {
      toast.style.transition =
        "all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)";
      toast.style.transform = "translateX(0) scale(1)";
      toast.style.opacity = "1";
    }, 10);

    // Show toast with premium timing
    const bsToast = new bootstrap.Toast(toast, {
      delay: duration,
      autohide: true
    });
    bsToast.show();

    // Premium progress bar animation
    if (progress) {
      const progressBar = toast.querySelector(".toast-progress");
      if (progressBar) {
        progressBar.style.animation = `progress-countdown ${duration}ms linear`;
      }
    }

    // Remove from DOM after hiding with exit animation
    toast.addEventListener("hidden.bs.toast", () => {
      toast.style.transform = "translateX(100%) scale(0.8)";
      toast.style.opacity = "0";
      setTimeout(() => toast.remove(), 300);
    });

    return toast;
  }

  createToast(title, message, type, emoji, duration, progress = true) {
    const toast = document.createElement("div");
    toast.className = `toast modern-toast ${type}`;
    toast.setAttribute("role", "alert");

    const progressHTML = progress ? '<div class="toast-progress"></div>' : "";

    toast.innerHTML = `
            <div class="toast-header">
                <span class="toast-emoji">${emoji}</span>
                <strong class="me-auto">${title}</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
            ${progressHTML}
        `;

    // Add premium progress bar styles
    if (progress) {
      const style = document.createElement("style");
      style.textContent = `
        .toast-progress {
          position: absolute;
          bottom: 0;
          left: 0;
          height: 3px;
          background: rgba(255, 255, 255, 0.8);
          border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
          transform-origin: left;
        }
        @keyframes progress-countdown {
          0% { width: 100%; }
          100% { width: 0%; }
        }
      `;
      if (!document.querySelector("#toast-progress-styles")) {
        style.id = "toast-progress-styles";
        document.head.appendChild(style);
      }
    }

    return toast;
  }

  getDefaultTitle(type) {
    const titles = {
      success: "🎉 Success!",
      error: "❌ Oops!",
      warning: "⚠️ Warning",
      info: "💡 Info"
    };
    return titles[type] || "Notification";
  }

  getDefaultEmoji(type) {
    const emojis = {
      success: "✅",
      error: "🚫",
      warning: "⚠️",
      info: "ℹ️"
    };
    return emojis[type] || "📢";
  }

  getVibrationPattern(type) {
    const patterns = {
      success: [100, 50, 100],
      error: [200, 100, 200, 100, 200],
      warning: [150, 75, 150],
      info: [100]
    };
    return patterns[type] || [100];
  }

  // Convenience methods
  success(message, options = {}) {
    return this.show(message, "success", options);
  }

  error(message, options = {}) {
    return this.show(message, "error", options);
  }

  warning(message, options = {}) {
    return this.show(message, "warning", options);
  }

  info(message, options = {}) {
    return this.show(message, "info", options);
  }
}

// Initialize notifications
const notifications = new ModernNotifications();

// Confetti Effect
function createConfetti() {
  const confetti = document.createElement("div");
  confetti.className = "confetti";
  document.body.appendChild(confetti);

  const colors = [
    "#4361ee",
    "#4cc9f0",
    "#72efdd",
    "#80ffdb",
    "#4895ef",
    "#3f37c9",
    "#7209b7"
  ];

  for (let i = 0; i < 50; i++) {
    const piece = document.createElement("div");
    piece.className = "confetti-piece";
    piece.style.left = Math.random() * 100 + "%";
    piece.style.backgroundColor =
      colors[Math.floor(Math.random() * colors.length)];
    piece.style.animationDelay = Math.random() * 3 + "s";
    piece.style.animationDuration = Math.random() * 3 + 2 + "s";
    confetti.appendChild(piece);
  }

  setTimeout(() => {
    confetti.remove();
  }, 5000);
}

// Enhanced OTP Input System
function createModernOTPInput(container, length = 6) {
  container.innerHTML = "";
  container.className = "otp-container";

  const inputs = [];

  for (let i = 0; i < length; i++) {
    const input = document.createElement("input");
    input.type = "text";
    input.className = "otp-digit";
    input.maxLength = 1;
    input.inputMode = "numeric";
    input.pattern = "[0-9]";
    input.dataset.index = i;

    // Event listeners
    input.addEventListener("input", handleOTPInput);
    input.addEventListener("keydown", handleOTPKeydown);
    input.addEventListener("paste", handleOTPPaste);

    container.appendChild(input);
    inputs.push(input);
  }

  // Focus first input
  inputs[0].focus();

  return inputs;
}

function handleOTPInput(e) {
  const input = e.target;
  const value = input.value.replace(/\D/g, "");
  input.value = value;

  if (value) {
    input.classList.add("filled");
    // Move to next input
    const nextInput = input.parentNode.querySelector(
      `[data-index="${parseInt(input.dataset.index) + 1}"]`
    );
    if (nextInput) {
      nextInput.focus();
    } else {
      // All inputs filled, trigger submission
      const allInputs = input.parentNode.querySelectorAll(".otp-digit");
      const otp = Array.from(allInputs).map(inp => inp.value).join("");
      if (otp.length === allInputs.length) {
        handleOTPComplete(otp);
      }
    }
  } else {
    input.classList.remove("filled");
  }
}

function handleOTPKeydown(e) {
  const input = e.target;

  if (e.key === "Backspace" && !input.value) {
    const prevInput = input.parentNode.querySelector(
      `[data-index="${parseInt(input.dataset.index) - 1}"]`
    );
    if (prevInput) {
      prevInput.focus();
      prevInput.value = "";
      prevInput.classList.remove("filled");
    }
  }
}

function handleOTPPaste(e) {
  e.preventDefault();
  const paste = (e.clipboardData || window.clipboardData).getData("text");
  const numbers = paste.replace(/\D/g, "");
  const inputs = e.target.parentNode.querySelectorAll(".otp-digit");

  for (let i = 0; i < Math.min(numbers.length, inputs.length); i++) {
    inputs[i].value = numbers[i];
    inputs[i].classList.add("filled");
  }

  if (numbers.length >= inputs.length) {
    handleOTPComplete(numbers.substring(0, inputs.length));
  }
}

function handleOTPComplete(otp) {
  // Add completion animation
  const inputs = document.querySelectorAll(".otp-digit");
  inputs.forEach((input, index) => {
    setTimeout(() => {
      input.style.transform = "scale(1.1)";
      setTimeout(() => {
        input.style.transform = "scale(1)";
      }, 150);
    }, index * 50);
  });

  // Show success notification
  notifications.success("OTP entered successfully! 🎯", {
    title: "🔥 Nice!",
    duration: 2000
  });

  // Submit form after animation
  setTimeout(() => {
    const form = document.querySelector("form");
    if (form) {
      // Create hidden input with OTP value
      const hiddenInput = document.createElement("input");
      hiddenInput.type = "hidden";
      hiddenInput.name = "otp";
      hiddenInput.value = otp;
      form.appendChild(hiddenInput);
      form.submit();
    }
  }, 500);
}

document.addEventListener("DOMContentLoaded", function() {
  // 🚀 Initialize Premium Features
  const loader = new PremiumLoader();
  const particles = new PremiumParticles();

  // Premium welcome message
  setTimeout(() => {
    notifications.info("Welcome to the future of authentication! ✨", {
      title: "🚀 Premium Experience",
      emoji: "🎯",
      duration: 8000
    });
  }, 2500);

  // Initialize Bootstrap tooltips with premium styling
  const tooltipTriggerList = [].slice.call(
    document.querySelectorAll('[data-bs-toggle="tooltip"]')
  );
  tooltipTriggerList.map(function(tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl, {
      customClass: "premium-tooltip"
    });
  });

  // Initialize Bootstrap popovers with premium styling
  const popoverTriggerList = [].slice.call(
    document.querySelectorAll('[data-bs-toggle="popover"]')
  );
  popoverTriggerList.map(function(popoverTriggerEl) {
    return new bootstrap.Popover(tooltipTriggerEl, {
      customClass: "premium-popover"
    });
  });

  // Handle phone number input formatting
  const phoneInput = document.getElementById("phone_number");
  if (phoneInput) {
    phoneInput.addEventListener("input", function(e) {
      // Basic validation for phone number input
      // This is a simple implementation - consider using a library like libphonenumber-js for production
      let input = e.target.value.replace(/\D/g, "");
      if (input.length > 0 && !input.startsWith("+")) {
        input = "+" + input;
      }

      // Limit length
      if (input.length > 15) {
        input = input.substring(0, 15);
      }

      e.target.value = input;
    });
  }

  // Enhanced OTP input handling
  const otpInput = document.getElementById("otp");
  const otpContainer = document.querySelector(".otp-input-container");

  if (otpInput && otpContainer) {
    // Replace single input with modern OTP inputs
    createModernOTPInput(otpContainer, 6);

    // Show welcome notification for OTP page
    notifications.info("Enter the 6-digit code sent to your phone 📱", {
      title: "🔐 Verification Time!",
      emoji: "🎯",
      duration: 8000
    });
  } else if (otpInput) {
    // Fallback for existing single input
    otpInput.focus();

    // Only allow numbers
    otpInput.addEventListener("input", function(e) {
      this.value = this.value.replace(/\D/g, "");

      // Add visual feedback with animations
      if (this.value.length === 6) {
        this.classList.add("is-valid", "pulse-animation");

        // Show completion notification
        notifications.success("Ready to verify! 🚀", {
          title: "🎯 Complete!",
          duration: 2000
        });

        // Auto-submit after short delay
        setTimeout(() => {
          document.querySelector("form").submit();
        }, 1000);
      } else {
        this.classList.remove("is-valid", "pulse-animation");
      }
    });

    // Start countdown timer for OTP expiration
    let timeLeft = 300; // 5 minutes in seconds
    const timerElement = document.getElementById("otp-timer");

    if (timerElement) {
      const countdownTimer = setInterval(function() {
        const minutes = Math.floor(timeLeft / 60);
        const seconds = timeLeft % 60;

        timerElement.textContent = `${minutes}:${seconds < 10
          ? "0"
          : ""}${seconds}`;

        if (timeLeft <= 0) {
          clearInterval(countdownTimer);
          timerElement.textContent = "Expired";
          timerElement.classList.add("text-danger");

          // Optionally disable the form
          const submitButton = document.querySelector('button[type="submit"]');
          if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = "OTP Expired";
          }
        }

        timeLeft--;
      }, 1000);
    }
  }

  // Convert traditional flash messages to modern notifications
  const flashMessages = document.querySelectorAll(".alert");
  flashMessages.forEach(function(flash) {
    const message = flash.textContent.trim();
    const type = flash.classList.contains("alert-success")
      ? "success"
      : flash.classList.contains("alert-danger")
        ? "error"
        : flash.classList.contains("alert-warning") ? "warning" : "info";

    // Hide original flash message
    flash.style.display = "none";

    // Show modern notification
    setTimeout(() => {
      notifications.show(message, type, {
        duration: 10000,
        sound: true,
        vibrate: true
      });

      // Add confetti for success messages
      if (type === "success" && message.toLowerCase().includes("success")) {
        createConfetti();
      }
    }, 100);
  });

  // Add animation to dashboard cards
  const dashboardCards = document.querySelectorAll(".dashboard-card");
  dashboardCards.forEach(function(card, index) {
    setTimeout(function() {
      card.classList.add("show");
    }, index * 100); // Stagger animation
  });

  // Add welcome notification for dashboard
  if (window.location.pathname === "/dashboard") {
    setTimeout(() => {
      notifications.success("Welcome to your secure dashboard! 🏠", {
        title: "🎉 You're in!",
        emoji: "🔐",
        duration: 8000
      });
    }, 500);
  }

  // Add engaging button interactions
  const buttons = document.querySelectorAll(".btn");
  buttons.forEach(button => {
    button.addEventListener("click", function(e) {
      // Add ripple effect
      const ripple = document.createElement("span");
      const rect = this.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;

      ripple.style.width = ripple.style.height = size + "px";
      ripple.style.left = x + "px";
      ripple.style.top = y + "px";
      ripple.classList.add("ripple");

      this.appendChild(ripple);

      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  });

  // Add loading states for forms
  const forms = document.querySelectorAll("form");
  forms.forEach(form => {
    form.addEventListener("submit", function(e) {
      const submitBtn = this.querySelector('button[type="submit"]');
      if (submitBtn && !submitBtn.disabled) {
        const originalText = submitBtn.textContent;
        submitBtn.innerHTML = '<span class="loading-dots">Processing</span>';
        submitBtn.disabled = true;

        // Re-enable after timeout (fallback)
        setTimeout(() => {
          submitBtn.textContent = originalText;
          submitBtn.disabled = false;
        }, 10000);
      }
    });
  });
});

// Function to toggle password visibility
function togglePasswordVisibility(inputId, iconId) {
  const passwordInput = document.getElementById(inputId);
  const icon = document.getElementById(iconId);

  if (passwordInput.type === "password") {
    passwordInput.type = "text";
    icon.classList.remove("fa-eye");
    icon.classList.add("fa-eye-slash");
  } else {
    passwordInput.type = "password";
    icon.classList.remove("fa-eye-slash");
    icon.classList.add("fa-eye");
  }
}
