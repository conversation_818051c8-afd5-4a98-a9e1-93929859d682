{% extends 'base.html' %}

{% block title %}Login - OTP Authentication{% endblock %}

{% block content %}
<div class="row justify-content-center align-items-center">
    <div class="col-md-6 col-lg-5 order-md-2">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center py-3">
                <h4 class="mb-0"><i class="fas fa-sign-in-alt me-2"></i>Login</h4>
            </div>
            <div class="card-body p-4">
                <form method="POST" action="{{ url_for('auth.login') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="identifier" class="form-label">Username or Phone Number</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="identifier" name="identifier"
                                placeholder="Enter username or phone number" required>
                        </div>
                        <div class="form-text">You can login with either your username or phone number</div>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Send OTP</button>
                    </div>
                </form>
            </div>
            <div class="card-footer bg-light text-center py-3">
                <p class="mb-0">Don't have an account? <a href="{{ url_for('auth.register') }}">Register</a></p>
                {% if config.DEBUG %}
                <p class="mb-0 mt-2">
                    <small class="text-muted">Development: </small>
                    <a href="{{ url_for('auth.unlock_account') }}" class="text-warning">
                        <i class="fas fa-unlock"></i> Unlock Account
                    </a>
                </p>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-6 col-lg-5 order-md-1 text-center mb-4 mb-md-0">
        <img src="{{ url_for('static', filename='images/login-illustration.svg') }}" alt="Login Illustration"
            class="img-fluid illustration-image">
        <h3 class="mt-3 text-primary">Welcome Back!</h3>
        <p class="text-muted">Enter your credentials to access your secure account</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Welcome notification for login page
        setTimeout(() => {
            if (typeof notifications !== 'undefined') {
                notifications.info('Ready to sign in? Enter your credentials below! 🚀', {
                    title: '👋 Welcome Back!',
                    emoji: '🔐',
                    duration: 8000
                });
            }
        }, 500);

        // Add form submission notification
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function (e) {
                const identifier = document.getElementById('identifier').value.trim();
                if (identifier) {
                    if (typeof notifications !== 'undefined') {
                        notifications.info(`Sending OTP to your phone... 📱`, {
                            title: '📤 Processing...',
                            duration: 6000
                        });
                    }
                }
            });
        }

        // Add input focus animations
        const identifierInput = document.getElementById('identifier');
        if (identifierInput) {
            identifierInput.addEventListener('focus', function () {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'all 0.3s ease';
            });

            identifierInput.addEventListener('blur', function () {
                this.parentElement.style.transform = 'scale(1)';
            });
        }
    });
</script>
{% endblock %}